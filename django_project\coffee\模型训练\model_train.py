import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os

plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

def create_features(df):
    """创建特征"""
    # 时间特征
    df['hour'] = pd.to_datetime(df['时间']).dt.hour
    df['dayofweek'] = df['order_date'].dt.dayofweek
    df['quarter'] = df['order_date'].dt.quarter
    df['month'] = df['order_date'].dt.month
    df['year'] = df['order_date'].dt.year
    
    # 编码分类特征
    label_encoders = {}
    categorical_features = ['支付方式', '会员等级', '产品名称']
    
    for feature in categorical_features:
        label_encoders[feature] = LabelEncoder()
        df[f'{feature}_encoded'] = label_encoders[feature].fit_transform(df[feature])
    
    return df, label_encoders

def train_model(data_path):
    """训练线性回归模型进行时序预测"""
    # 创建result文件夹（如果不存在）
    if not os.path.exists('result'):
        os.makedirs('result')

    # 读取数据
    data = pd.read_csv(data_path)
    
    # 将日期列转换为datetime类型
    data['order_date'] = pd.to_datetime(data['日期'])
    
    # 创建特征
    data, label_encoders = create_features(data)
    
    # 按日期汇总并计算特征均值
    daily_features = data.groupby('order_date').agg({
        'hour': 'mean',
        'dayofweek': 'first',
        'quarter': 'first',
        'month': 'first',
        'year': 'first',
        '支付方式_encoded': 'mean',
        '会员等级_encoded': 'mean',
        '产品名称_encoded': 'mean',
        '数量': 'mean',
        '折扣率': 'mean',
        '实付金额': 'sum'
    }).reset_index()
    
    # 准备特征和目标变量
    feature_columns = ['hour', 'dayofweek', 'quarter', 'month', 'year',
                      '支付方式_encoded', '会员等级_encoded', '产品名称_encoded',
                      '数量', '折扣率']
    
    X = daily_features[feature_columns]
    y = daily_features['实付金额']
    
    # 创建并训练线性回归模型
    model = LinearRegression()
    model.fit(X, y)
    
    # 预测训练集
    y_pred = model.predict(X)
    
    # 计算评估指标
    mse = mean_squared_error(y, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y, y_pred)
    r2 = r2_score(y, y_pred)
    
    # 预测未来7天
    last_date = data['order_date'].max()
    future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=7, freq='D')
    
    # 创建未来数据的特征
    future_df = pd.DataFrame({'order_date': future_dates})
    future_df['hour'] = data['hour'].mean()
    future_df['dayofweek'] = future_df['order_date'].dt.dayofweek
    future_df['quarter'] = future_df['order_date'].dt.quarter
    future_df['month'] = future_df['order_date'].dt.month
    future_df['year'] = future_df['order_date'].dt.year
    future_df['支付方式_encoded'] = data['支付方式_encoded'].mean()
    future_df['会员等级_encoded'] = data['会员等级_encoded'].mean()
    future_df['产品名称_encoded'] = data['产品名称_encoded'].mean()
    future_df['数量'] = data['数量'].mean()
    future_df['折扣率'] = data['折扣率'].mean()
    
    # 使用模型预测
    future_X = future_df[feature_columns]
    future_pred = model.predict(future_X)
    
    # 计算预测区间
    pred_std = np.std(y - y_pred)
    future_df['yhat'] = future_pred
    future_df['yhat_lower'] = future_pred - 1.96 * pred_std
    future_df['yhat_upper'] = future_pred + 1.96 * pred_std
    
    # 绘制预测结果图
    plt.figure(figsize=(12, 6))
    plt.plot(daily_features['order_date'], daily_features['实付金额'], label='实际销售额')
    plt.plot(future_dates, future_pred, label='预测销售额', color='red')
    plt.fill_between(future_dates, 
                     future_df['yhat_lower'], 
                     future_df['yhat_upper'], 
                     color='red', 
                     alpha=0.1)
    plt.title('销售额预测')
    plt.xlabel('日期')
    plt.ylabel('销售额')
    plt.legend()
    plt.tight_layout()
    plt.savefig('result/forecast_plot.png')
    plt.close()
    
    # 保存模型评估指标
    with open('result/model_evaluation.txt', 'w', encoding='utf-8') as f:
        f.write(f'均方误差 (MSE): {mse:.4f}\n')
        f.write(f'均方根误差 (RMSE): {rmse:.4f}\n')
        f.write(f'平均绝对误差 (MAE): {mae:.4f}\n')
        f.write(f'决定系数 (R²): {r2:.4f}\n')
        f.write('\n模型系数:\n')
        for feature, coef in zip(feature_columns, model.coef_):
            f.write(f'{feature}: {coef:.4f}\n')
        f.write(f'\n截距: {model.intercept_:.4f}')
    
    # 保存模型和编码器
    import joblib
    joblib.dump((model, pred_std, label_encoders), 'result/linear_regression_model.joblib')
    
    # 保存最近的预测结果
    future_df.to_csv('result/latest_forecast.csv', index=False)
    
    return r2

if __name__ == '__main__':
    train_model('dataset/coffee_sales_data.csv')  # 这里是模型训练模块，训练结果保存在result文件夹当中